{"basicSettings": "基础设置", "fieldSettings": "字段设置", "pageSettings": "页面设置", "generationSettings": "生成配置", "associatedConfiguration": "关联设置", "tableName": "表名称", "tableContent": "描述", "addon": "所属插件", "moduleName": "模块名", "className": "类名", "editType": "编辑方式", "createTime": "创建时间", "updateTime": "更新时间", "popup": "弹出", "page": "新页面", "selectPlaceholder": "请选择", "tableNamePlaceholder": "请输入表名", "tableContentPlaceholder": "请输入描述", "addonPlaceholder": "请选择插件", "addonPlaceholder1": "/", "moduleNamePlaceholder": "请输入模块名", "classNamePlaceholder": "请输入类名", "editTypePlaceholder": "请选择编辑方式", "columnNamePlaceholder": "请输入字段名", "columnCommentPlaceholder": "请输入字段描述", "tableNameValidata": "表名不符合规范，请以 字母、_ 开头，不能出现 字母、数字、下划线 以外的字符", "fieldNameValidata": "不符合规范，请以 字母、_ 开头，不能出现 字母、数字、下划线 以外的字符", "addCode": "导入数据表", "updateCode": "编辑代码生成", "codeDeleteTips": "确定要删除吗？", "tableComment": "表描述", "tableCreateTime": "创建时间", "tableUpdateTime": "修改时间", "addBtn": "添加", "searchPlaceholder": "请输入表名或表描述搜索", "selectTableTips": "确认导入该数据表吗？", "download": "下载", "commentField": "常用字段", "baseField": "公用字段", "columnName": "字段列名", "columnComment": "字段描述", "columnType": "字段类型", "fieldAttribute": "字段属性", "addAndEdit": "添加编辑", "listSearch": "列表查询", "isPk": "主键", "isRequired": "必填", "isInsert": "添加", "isUpdate": "编辑", "isLists": "列表", "isSearch": "搜索", "isQuery": "查询", "queryType": "搜索方式", "formType": "表单类型", "verifyType": "验证类型", "formInput": "文本框", "formTextarea": "文本域", "formSelect": "下拉框", "formRadio": "单选框", "formCheckbox": "复选框", "formDateTime": "日期", "formImageSelect": "图片上传", "formFileSelect": "文件上传", "formVideoSelect": "视频上传", "formEditor": "富文本", "formNumber": "数字框", "pk": "主键", "status": "状态", "string": "字符串", "image": "图片", "radio": "单选", "checkbox": "复选", "select": "下拉选择", "editor": "富文本", "dateTime": "日期", "formValidation": "表单验证", "deleteType": "删除类型", "physicalDeletion": "物理删除", "softDeletion": "软删除", "deleteField": "删除字段", "deleteFieldPlaceholder": "请选择删除字段", "orderColumnName": "排序字段", "orderColumnNamePlaceholder": "请选择排序字段", "orderType": "排序方式", "orderTypePlaceholder": "请选择排序方式", "menuType": "上级菜单", "autoBuild": "自动构建", "manualAddition": "手动添加", "controller": "控制器目录", "dataModel": "模型目录", "validator": "验证器目录", "webView": "WEB端视图目录", "routerView": "路由目录", "insertAssociated": "新增关联", "associatedType": "关联类型", "associatedTypePlaceholder": "请选择关联类型", "hasOne": "一对一", "hasMany": "一对多", "associatedName": "关联方法名称", "associatedNamePlaceholder": "请输入关联方法名称", "associatedModel": "关联模型", "associatedModelPlaceholder": "请选择关联模型", "localKey": "关联键", "localKeyPlaceholder": "请选择关联键", "foreignKey": "外键", "foreignKeyPlaceholder": "请输入外键", "addons": "关联应用", "addonsPlaceholder": "请选择应用", "saveAndSync": "同步代码", "saveAndDownload": "下载代码", "saveAndSyncText": "同步的代码与项目产生冲突，是否确认覆盖?", "saveAndSyncText1": "同步的代码会加入到项目代码中，是否确认继续", "mobileVerify": "手机号验证", "numberVerify": "整数验证", "idCardVerify": "身份证验证", "emailVerify": "邮箱验证", "maxVerify": "最大输入120个字符", "minVerify": "最小输入1个字符", "maxLabel": "最大输入字符", "minLabel": "最小输入字符", "minPlaceholder": "最小输入字符不能为空", "minPlaceholder1": "最小输入字符不可大于最大输入字符", "maxPlaceholder": "最大字输入字符不能为空", "maxPlaceholder1": "最大输入字符不可小于最小输入字符", "maxLabel1": "最大输入数", "minLabel1": "最小输入数", "min1Placeholder": "最小输入数不能为空", "min1Placeholder1": "最小输入数不可大于最大输入数", "max1Placeholder": "最大字输入数不能为空", "max1Placeholder1": "最大输入数不可小于最小输入数", "between": "输入字符区间", "setUp": "设置", "dictType": "数据字典", "dictTypePlaceholder": "请选择数据字典", "dictTypePlaceholder1": "部分字段未选择数据字典", "remotePullDown": "远程下拉", "remotePullDownValue": "远程下拉value字段", "remotePullDownValuePlaceholder": "请选择远程下拉value字段", "remotePullDownLabel": "远程下拉标题字段", "remotePullDownLabelPlaceholder": "请选择远程下拉label字段", "selectType": "下拉类型"}