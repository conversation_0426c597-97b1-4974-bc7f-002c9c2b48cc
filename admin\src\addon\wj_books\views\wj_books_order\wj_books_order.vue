<template>
    <div class="main-container">
        <el-card class="box-card !border-none" shadow="never">
            <div class="flex justify-between items-center">
                <span class="text-lg">{{pageName}}</span>
            </div>

            <el-card class="box-card !border-none my-[10px] table-search-wrap" shadow="never">
                <el-form :inline="true" :model="wjBooksOrderTable.searchParam" ref="searchFormRef">
                    <el-form-item :label="t('orderNo')" prop="order_no">
                        <el-input v-model="wjBooksOrderTable.searchParam.order_no" :placeholder="t('orderNoPlaceholder')" />
                    </el-form-item>

                    <el-form-item :label="t('status')" prop="status">
                        <el-select v-model="wjBooksOrderTable.searchParam.status" clearable :placeholder="t('statusPlaceholder')">
                            <el-option :label="t('statusWaitingPickup')" :value="1" />
                            <el-option :label="t('statusPickedUp')" :value="2" />
                            <el-option :label="t('statusAuditing')" :value="3" />
                            <el-option :label="t('statusCompleted')" :value="4" />
                            <el-option :label="t('statusCancelled')" :value="5" />
                        </el-select>
                    </el-form-item>

                    <el-form-item :label="t('createTime')" prop="create_time">
                        <el-date-picker
                            v-model="dateRange"
                            type="daterange"
                            range-separator="~"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="YYYY-MM-DD"
                            @change="handleDateChange"
                        />
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="loadWjBooksOrderList()">{{ t('search') }}</el-button>
                        <el-button @click="resetForm(searchFormRef)">{{ t('reset') }}</el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <div class="mt-[10px]">
                <el-table :data="wjBooksOrderTable.data" size="large" v-loading="wjBooksOrderTable.loading">
                    <template #empty>
                        <span>{{ !wjBooksOrderTable.loading ? t('emptyData') : '' }}</span>
                    </template>
                    
                    <el-table-column prop="order_no" :label="t('orderNo')" min-width="120" :show-overflow-tooltip="true"/>
                    
                    <el-table-column :label="t('bookInfo')" min-width="120">
                        <template #default="{ row }">
                            <div>{{ t('bookCount') }}: {{ row.book_count }}</div>
                            <div>{{ t('totalAmount') }}: ¥{{ row.total_amount }}</div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column :label="t('finalInfo')" min-width="120">
                        <template #default="{ row }">
                            <div v-if="row.final_book_count">{{ t('finalBookCount') }}: {{ row.final_book_count }}</div>
                            <div v-if="row.final_amount">{{ t('finalAmount') }}: ¥{{ row.final_amount }}</div>
                            <div v-if="!row.final_book_count && !row.final_amount">{{ t('notAudited') }}</div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="status" :label="t('status')" min-width="100">
                        <template #default="{ row }">
                            <el-tag :type="getStatusTagType(row.status)">
                                {{ formatStatus(row.status) }}
                            </el-tag>
                            <div v-if="row.status === 3" class="mt-1">
                                <el-progress 
                                    :percentage="row.audit_progress || 0" 
                                    :stroke-width="5"
                                    :text-inside="true"
                                    :show-text="false"
                                ></el-progress>
                                <div class="text-xs text-gray-500 mt-1">{{ row.audit_progress || 0 }}%</div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="pickup_time" :label="t('pickupTime')" min-width="120" :show-overflow-tooltip="true"/>
                    
                    <el-table-column prop="express_waybill" :label="t('expressWaybill')" min-width="120" :show-overflow-tooltip="true"/>

                    <el-table-column prop="create_time" :label="t('createTime')" min-width="120" :show-overflow-tooltip="true"/>

                    <el-table-column :label="t('operation')" fixed="right" min-width="180">
                       <template #default="{ row }">
                           <el-button type="primary" link @click="viewDetail(row)">{{ t('view') }}</el-button>
                           <el-button v-if="row.status === 1" type="success" link @click="updateStatus(row.id, 2)">{{ t('confirmPickup') }}</el-button>
                           <el-button v-if="row.status === 2" type="success" link @click="updateStatus(row.id, 3)">{{ t('startAudit') }}</el-button>
                           <el-button v-if="row.status === 1" type="danger" link @click="showCancelDialog(row)">{{ t('cancel') }}</el-button>
                       </template>
                    </el-table-column>
                </el-table>
                <div class="mt-[16px] flex justify-end">
                    <el-pagination 
                        v-model:current-page="wjBooksOrderTable.page" 
                        v-model:page-size="wjBooksOrderTable.limit"
                        layout="total, sizes, prev, pager, next, jumper" 
                        :total="wjBooksOrderTable.total"
                        @size-change="loadWjBooksOrderList()" 
                        @current-change="loadWjBooksOrderList" 
                    />
                </div>
            </div>

            <!-- 取消订单对话框 -->
            <el-dialog v-model="cancelDialog.visible" :title="t('cancelOrder')" width="500px">
                <el-form :model="cancelDialog.form" label-width="100px">
                    <el-form-item :label="t('cancelReason')" required>
                        <el-input 
                            v-model="cancelDialog.form.reason" 
                            type="textarea" 
                            :rows="3"
                            :placeholder="t('cancelReasonPlaceholder')"
                        ></el-input>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="cancelDialog.visible = false">{{ t('cancel') }}</el-button>
                        <el-button type="primary" @click="confirmCancel" :loading="cancelDialog.loading">
                            {{ t('confirm') }}
                        </el-button>
                    </span>
                </template>
            </el-dialog>

            <!-- 订单详情组件 -->
            <order-detail ref="orderDetailRef" @refresh="loadWjBooksOrderList" />
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { t } from '@/lang'
import { ElMessageBox, FormInstance } from 'element-plus'
import { getWjBooksOrderList, updateWjBooksOrderStatus, cancelWjBooksOrder, updateWjBooksOrderAuditProgress } from '@/addon/wj_books/api/wj_books_order'
import { useRoute, useRouter } from 'vue-router'
import OrderDetail from '@/addon/wj_books/views/wj_books_order/components/wj-books-order-detail.vue'

const route = useRoute()
const router = useRouter()
const pageName = route.meta.title

// 订单列表数据
const wjBooksOrderTable = reactive({
    page: 1,
    limit: 10,
    total: 0,
    loading: true,
    data: [],
    searchParam: {
        order_no: '',
        status: '',
        start_time: '',
        end_time: ''
    }
})

// 日期范围
const dateRange = ref([])

// 搜索表单引用
const searchFormRef = ref<FormInstance>()

// 订单详情组件引用
const orderDetailRef = ref()

// 取消订单对话框
const cancelDialog = reactive({
    visible: false,
    loading: false,
    orderId: null as number | null,
    form: {
        reason: ''
    }
})

/**
 * 加载订单列表
 */
const loadWjBooksOrderList = (page: number = 1) => {
    wjBooksOrderTable.loading = true
    wjBooksOrderTable.page = page

    getWjBooksOrderList({
        page: wjBooksOrderTable.page,
        limit: wjBooksOrderTable.limit,
        ...wjBooksOrderTable.searchParam
    }).then(res => {
        wjBooksOrderTable.loading = false
        wjBooksOrderTable.data = res.data.data
        wjBooksOrderTable.total = res.data.total
    }).catch(() => {
        wjBooksOrderTable.loading = false
    })
}

// 初始加载订单列表
loadWjBooksOrderList()

/**
 * 处理日期范围变化
 */
const handleDateChange = (val: any) => {
    if (val) {
        wjBooksOrderTable.searchParam.start_time = val[0]
        wjBooksOrderTable.searchParam.end_time = val[1]
    } else {
        wjBooksOrderTable.searchParam.start_time = ''
        wjBooksOrderTable.searchParam.end_time = ''
    }
}

/**
 * 重置表单
 */
const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    dateRange.value = []
    wjBooksOrderTable.searchParam.start_time = ''
    wjBooksOrderTable.searchParam.end_time = ''
    loadWjBooksOrderList()
}

/**
 * 格式化订单状态
 */
const formatStatus = (status: number) => {
    const statusMap: Record<number, string> = {
        1: t('statusWaitingPickup'),
        2: t('statusPickedUp'),
        3: t('statusAuditing'),
        4: t('statusCompleted'),
        5: t('statusCancelled')
    }
    return statusMap[status] || status
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: number) => {
    const typeMap: Record<number, string> = {
        1: 'warning',
        2: 'info',
        3: 'primary',
        4: 'success',
        5: 'danger'
    }
    return typeMap[status] || ''
}

/**
 * 查看订单详情
 */
const viewDetail = (row: any) => {
    orderDetailRef.value.showDetail(row.id)
}

/**
 * 更新订单状态
 */
const updateStatus = (id: number, status: number) => {
    let confirmMessage = ''
    if (status === 2) {
        confirmMessage = t('confirmPickupMessage')
    } else if (status === 3) {
        confirmMessage = t('startAuditMessage')
    }

    ElMessageBox.confirm(confirmMessage, t('warning'), {
        confirmButtonText: t('confirm'),
        cancelButtonText: t('cancel'),
        type: 'warning',
    }).then(() => {
        updateWjBooksOrderStatus(id, status).then(() => {
            // 如果是开始审核，初始化审核进度为0
            if (status === 3) {
                updateWjBooksOrderAuditProgress(id, 0)
            }
            loadWjBooksOrderList()
        })
    })
}

/**
 * 显示取消订单对话框
 */
const showCancelDialog = (row: any) => {
    cancelDialog.orderId = row.id
    cancelDialog.form.reason = ''
    cancelDialog.visible = true
}

/**
 * 确认取消订单
 */
const confirmCancel = () => {
    if (!cancelDialog.form.reason) {
        ElMessageBox.alert(t('cancelReasonRequired'), t('warning'))
        return
    }

    cancelDialog.loading = true
    cancelWjBooksOrder(cancelDialog.orderId as number, cancelDialog.form.reason).then(() => {
        cancelDialog.loading = false
        cancelDialog.visible = false
        loadWjBooksOrderList()
    }).catch(() => {
        cancelDialog.loading = false
    })
}
</script>

<style lang="scss" scoped>
/* 多行超出隐藏 */
.multi-hidden {
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
</style>
 