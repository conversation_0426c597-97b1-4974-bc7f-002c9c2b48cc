{"oplatformSetting": "开放平台设置", "appidPlaceholder": "请输入微信第三方平台AppId", "appSecretPlaceholder": "请输入微信第三方平台AppSecret", "tokenPlaceholder": "请输入消息校验Token", "aesKeyPlaceholder": "请输入消息加解密Key", "oplatformComm": "开放平台通信", "empowerStartDomain": "授权发起页域名", "empowerReceiveUrl": "授权事件接收URL", "messageValidationToken": "消息校验Token", "messageDecryptKey": "消息加解密Key", "messageReceiveUrl": "消息与事件接收URL", "wechatDomain": "公众号开发域名", "weappDomain": "小程序服务器域名", "weappBusinessDomain": "小程序业务域名", "oplatformBuilder": "开发者设置", "builderEmail": "开发者邮箱", "builderMobile": "开发者手机号", "builderQQ": "开发者QQ", "builderWx": "开发者微信", "messageDecryptKeyTips": "在代替公众号或小程序收发消息过程中使用。必须是长度为43位的字符串，只能是字母和数字。", "regenerate": "重新生成", "messagesReceiving": "消息与事件接收", "domainSetting": "域名配置", "developerWeappUpload": "开发小程序配置", "developerAppid": "开发小程序appid", "uploadKey": "代码上传密钥", "uploadKeyTips": "", "developAppid": "开发小程序APPID", "developAppidPlaceholder": "请输入开发小程序APPID", "uploadIpTips": "如果小程序代码上传开启了ip白名单设置，在ip白名单中添加ip：", "groupName": "站点套餐", "lastTime": "上次同步时间", "weappVersionUpdate": "同步模板库", "weappVersionUpdateRecord": "模板库同步记录", "createTime": "提交时间", "userVersion": "版本号", "failReason": "失败原因", "updateTips": " 1、同步小程序时系统通过已绑定的开发小程序同步至微信第三方平台的普通模板库中。\n 2、同步完成后，系统将自动为站点套餐下已授权的小程序提交代码。\n 3、一键同步功能支持按所有站点套餐进行批量同步，同时也可针对单个站点套餐单独操作。\n 4、使用此功能前，请确保已启动消息队列服务。", "seeUpdateRecord": "查看同步记录", "commitRecord": "同步记录", "oneClickSync": "一键同步", "syncTemplateError": "未能同步到模板库", "templateID": "模板ID", "siteWeappSync": "站点小程序同步", "syncSiteWeappTips": "是否要给该套餐下已授权小程序的站点提交代码？", "publicInfo": "公众平台信息", "publicType": "公众平台类型", "siteName": "站点名称", "authTime": "授权时间", "qrcode": "二维码"}