.form-item-frame{
	@apply overflow-hidden relative;
	.base-layout-one{
		@apply relative flex flex-col;
		.layout-one-label{
			@apply flex items-center;
			line-height: 1.3;
			margin-bottom: 16rpx;
			.is-hidden{
				@apply border-solid;
				font-size: 20rpx;
				line-height: 20rpx;
				border-width: 2rpx;
				border-color: #999;
				padding: 4rpx;
				border-radius: 6rpx;
				margin-left: 6rpx;
				color: #999;
			}
			.required{
				color: #ec0003;
				padding-top: 4rpx;
				margin-left: 4rpx;
			}
			.name{
				@apply overflow-ellipsis;
			}
		}
		.layout-one-remark{
			margin-bottom: 16rpx;
			line-height: 1.2;
		}
		.layout-one-error-message{
			font-size: 28rpx;
			color: red;
			margin-bottom: 16rpx;
		}
		.layout-one-content{
			display: flex;
			align-items: center;
			min-height: 80rpx;
			padding: 0 16rpx;
			border: 2rpx solid #F2F2F2;
			border-radius: 16rpx;
			box-sizing: border-box;
		}
		.layout-one-input-placeholder{
			color: var(--text-color-light9);
		}
		.layout-one-attribute-wrap{
			@apply flex items-center;
			margin-top: 16rpx;
			.layout-one-attribute-item{
				font-size: 24rpx;
				color: #999;
				margin-right: 6rpx;
			}
		}
		.detail-one-content{
			padding: 5rpx 10rpx;
			display: flex;
			margin-bottom: 30rpx;
			flex-direction: column;
			.detail-one-content-label{
				color: #888;
				font-size: 26rpx;
				margin-bottom: 16rpx;
				line-height: 1;
			}
			.detail-one-content-value{
				font-size: 28rpx;
				line-height: 1.4;
				word-wrap: break-word;
			}
		}
	}
	.base-layout-two{
		@apply relative;
		.layout-two-is-hidden{
			font-size: 20rpx;
			border: 2rpx solid #999;
			padding: 4rpx;
			border-radius: 6rpx;
			margin-left: 6rpx;
			color: #999;
		}
		.layout-two-wrap{
			@apply flex items-center border-solid box-border;
			border-width: 0;
			border-bottom-width: 2rpx;
			border-color: #F2F2F2;
			padding: 0 16rpx;
			.layout-two-label{
				@apply flex items-center;
				width: 27%;
				padding-right: 20rpx;
				.required{
					color: #ec0003;
					padding-top: 4rpx;
					margin-right: 6rpx;
				}
				.name{
					@apply overflow-ellipsis;
				}
			}
			.layout-two-content{
				width: 73%;
				min-height: 90rpx;
				text-align: right;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				&.no-flex{
					display: block;
				}
			}
			.layout-two-input-placeholder{
				color: var(--text-color-light9);
			}
			&.no-border{
				padding: 0;
				border-width: 0;
				.layout-two-content{
					min-height: 72rpx;
				}
			}
		}
		.layout-two-remark{
			margin-top: 16rpx;
			line-height: 1.2;
		}
		.layout-two-error-message{
			font-size: 28rpx;
			color: red;
			margin-bottom: 16rpx;
		}
		.layout-two-attribute-wrap{
			@apply flex items-center;
			margin-top: 16rpx;
			.layout-two-attribute-item{
				font-size: 24rpx;
				color: #999;
				margin-right: 6rpx;
			}
		}
		.detail-two-content{
			// padding: 0rpx 10rpx; 
			line-height: 1;
			font-size: 28rpx;
			margin-bottom: 34rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.detail-two-content-label{
				
			}
			.detail-two-content-value {
				line-height: 1.4;
			    word-wrap: break-word; 
			    word-break: break-word; 
			    white-space: pre-wrap;
			    text-align: right;
				align-items: center;
			}
		
		}
	}
	.form-item-mask{
		@apply absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 10;
	}
}