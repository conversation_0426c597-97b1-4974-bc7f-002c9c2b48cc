{"search": "搜索应用名称", "appName": "应用名/版本信息", "introduction": "简介", "type": "类型", "app": "应用", "addon": "插件", "noPlug": "暂无应用", "install": "安装", "unload": "卸载", "installLabel": "已安装", "uninstalledLabel": "未安装", "version": "版本", "title": "名称", "desc": "简介", "plugDetail": "插件信息", "author": "作者", "detail": "详情", "addonInstall": "插件安装", "dirPermission": "目录读写权限", "path": "路径", "demand": "要求", "readable": "可读", "write": "可写", "packageManageTool": "包管理工具", "name": "名称", "addonInstallSuccess": "插件安装成功", "envCheck": "环境检查", "installProgress": "安装进度", "installComplete": "安装完成", "localAppText": "插件管理", "marketAppText": "官方市场", "installShowDialogCloseTips": "安装任务尚未完成，关闭将取消安装任务，是否要继续关闭？", "marketDevelopMessage": "官方市场正在开发中！", "jobError": "任务队列未启动 请在服务端源码部署目录打开终端执行 php think queue:listen", "conflictFiles": "冲突文件", "process": "启动进程", "open": "开启", "down": "下载", "addonVersion": "插件版本", "versionCode": "版本号", "createTime": "发布时间", "buyLabel": "已购买", "installTips": "安装后需手动更新插件引用的依赖和编译各个端口的前端源码", "localInstall": "本地安装", "cloudInstall": "一键云安装", "cloudInstallTips": "云安装可实现一键安装，安装后无需手动更新依赖和编译前端源码", "installingTips": "有插件正在安装中请等待安装完成之后再进行其他操作，点击查看", "installPercent": "安装进度", "downloading": "下载中", "authTips": "云安装需先绑定授权码，如果已有授权请先进行绑定，没有授权可到niucloud官网购买云服务之后再进行操作", "toBind": "绑定授权", "toNiucloud": "去niucloud官网", "descriptionLeft": "暂无任何应用，马上去", "link": "官方应用市场", "descriptionRight": "逛逛", "installed-empty": "暂未安装任何应用，请先安装", "siteAddressTips": "授权域名不匹配", "authCodePlaceholder": "请输入授权码", "authSecretPlaceholder": "请输入授权秘钥", "updateCode": "重新绑定", "notHaveAuth": "还没有授权？去购买", "authInfoTips": "授权码和授权秘钥可在Niucloud官网我的授权 授权详情中查看", "addonUninstall": "插件卸载", "appIdentification": "应用标识", "tipText": "标识指开发应用或插件的文件夹名称"}