{"weappName": "小程序名称", "weappOriginal": "小程序原始ID", "weappQrcode": "小程序二维码", "weappQrcodeTips": "建议尺寸上传430px*430px宽高的二维码", "weappAppid": "AppID(小程序ID)", "weappAppidTips": "AppID是小程序开发识别码，配合AppSecret可调用小程序的接口能力。", "weappAppsecret": "AppS<PERSON><PERSON>(小程序密钥)", "weappAppsecretTips": "AppSecret是校验小程序开发者身份的密钥，具有极高的安全性", "businessDomain": "业务域名", "requestUrl": "request合法域名", "socketUrl": "socket合法域名", "uploadUrl": "uploadFile合法域名", "downloadUrl": "downloadFile合法域名", "weappInfo": "小程序信息", "weappDevelopInfo": "小程序开发信息", "theServerSetting": "消息推送配置", "functionSetting": "服务器域名设置", "tokenTips": "必须为英文或数字，长度为3-32字符。", "encodingAESKeyTips": "消息加密密钥由43位字符组成，可随机修改，字符范围为A-Z，a-z，0-9。", "encryptionType": "消息加解密方式", "cleartextMode": "明文模式", "compatibleMode": "兼容模式", "safeMode": "安全模式（推荐）", "weappNamePlaceholder": "请输入小程序名称", "weappOriginalPlaceholder": "请输入小程序原始ID", "appidPlaceholder": "请输入开发者ID", "appSecretPlaceholder": "请输入开发者密码", "tokenPlaceholder": "请输入Token", "encodingAesKeyPlaceholder": "请输入EncodingAESKey", "cleartextModeTips": "明文模式下，不使用消息体加解密功能，安全系数较低", "compatibleModeTips": "兼容模式下，明文、密文将共存，方便开发者调试和维护", "safeModeTips": "安全模式下，消息包为纯密文，需要开发者加密和解密，安全系数高", "weappUpload": "小程序代码上传", "uploadKey": "上传密钥", "uploadKeyTips": "配置之后可实现在线上传小程序版本", "uploadIpTips": "如果小程序代码上传开启了ip白名单设置，在ip白名单中添加ip：", "update": "修改", "udpUrl": "udp合法域名", "tcpUrl": "tcp合法域名", "requestdomainPlaceholder": "以 https:// 开头。域名间请用 ; 分割", "wsrequestdomainPlaceholder": "以 wss:// 开头。域名间请用 ; 分割", "uploaddomainPlaceholder": "以 https:// 开头。域名间请用 ; 分割", "downloaddomainPlaceholder": "以 https:// 开头。域名间请用 ; 分割", "udpdomainPlaceholder": "以 udp:// 开头。域名间请用 ; 分割", "tcpdomainPlaceholder": "以 tcp:// 开头。域名间请用 ; 分割", "domainError": "该域名协议头非法", "serviceContentStatement": "服务内容声明", "privacyAgreement": "用户隐私保护指引", "privacyAgreementTips": "基于微信提供的 标准化用户隐私保护指引，根据小程序实际情况更新并展示给用户。", "setting": "设置", "privacyAgreementTitle": "用户隐私保护指引设置", "settingPlaceholder": "请填写用途", "addSettingType": "增加信息类型", "settingTypeTitle": "使用用户信息类型", "addContact": "增加联系方式", "addSdkInfo": "增加第三方SDK信息", "addSdkSettingList": "增加SDK提供方的隐私信息"}