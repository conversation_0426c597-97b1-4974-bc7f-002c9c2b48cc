{"aliappSet": "小程序设置", "aliappName": "小程序名称", "aliappOriginal": "APPID", "aliappQrcode": "小程序二维码", "aliappQrcodeTips": "建议尺寸上传430px*430px宽高的二维码", "aliappAppid": "应用私钥", "aliappAppsecret": "开发者密码", "downloadUrl": "downloadFile合法域名", "aliappInfo": "小程序信息", "aliappDevelopInfo": "开发设置", "theServerSetting": "接口内容加密设置", "functionSetting": "服务器配置信息", "encryptionType": "消息加解密方式", "compatibleMode": "兼容模式", "safeMode": "安全模式（推荐）", "aliappNamePlaceholder": "请输入小程序名称", "aliappOriginalPlaceholder": "请输入APPID", "appidPlaceholder": "请输入应用私钥", "tokenPlaceholder": "请输入Token", "encodingAesKeyPlaceholder": "请输入EncodingAESKey", "countersignType": "接口加签方式", "certificate": "证书", "publicKey": "应用公钥证书", "alipayPublicKey": "支付宝公钥证书", "alipayWithCrt": "支付宝根证书", "publicKeyTips": "上传appCertPublicKey_***.crt文件", "alipayPublicKeyTips": "上传alipayCertPublicKey_RSA2.crt文件", "alipayWithCrtTips": "上传alipayRootCert.crt文件", "AESKey": "AES秘钥", "serveWhiteList": "服务器域名白名单", "AESKeyPlaceholder": "请输入AES秘钥"}