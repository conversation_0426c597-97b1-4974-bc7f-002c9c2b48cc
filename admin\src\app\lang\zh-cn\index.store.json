{"search": "搜索应用名称", "appName": "应用名/版本信息", "introduction": "简介", "type": "类型", "app": "应用", "addon": "插件", "noPlug": "暂无应用", "install": "安装", "unload": "卸载", "installLabel": "已安装", "uninstalledLabel": "未安装", "version": "版本", "title": "名称", "desc": "简介", "plugDetail": "插件信息", "author": "作者", "detail": "详情", "addonInstall": "插件安装", "dirPermission": "目录读写权限", "path": "路径", "demand": "要求", "readable": "可读", "write": "可写", "packageManageTool": "包管理工具", "name": "名称", "addonInstallSuccess": "插件安装成功", "envCheck": "环境检查", "installProgress": "安装进度", "installComplete": "安装完成", "localAppText": "插件管理", "marketAppText": "官方市场", "installShowDialogCloseTips": "安装任务尚未完成，关闭将取消安装任务，是否要继续关闭？", "marketDevelopMessage": "官方市场正在开发中！", "jobError": "任务队列未启动 请在服务端源码部署目录打开终端执行 php think queue:listen", "conflictFiles": "冲突文件", "process": "启动进程", "open": "开启", "down": "下载", "installDown": "未安装(重新下载)", "unloadDown": "已安装(重新下载)", "addonVersion": "插件版本", "versionCode": "版本号", "createTime": "发布时间", "buyLabel": "已购买", "recentlyUpdated": "最近更新", "installTips": "本地安装过程仅对应用和插件的程序代码和数据库进行安装处理，并不会对前端代码进行编译，本地安装之后，必须对各前端端口进行编译，才能正常使用。", "localInstall": "本地安装", "cloudInstall": "一键云安装", "cloudInstallTips": "云安装可实现一键安装，云安装不仅会把应用和插件的程序代码安装处理，同时，会在云端编译各前端代码。云安装完成之后即可正常使用程序！ ", "installingTips": "有插件正在安装中请等待安装完成之后再进行其他操作，点击查看", "installPercent": "安装进度", "downloading": "下载中", "authTips": "云安装需先绑定授权码，如果已有授权请先进行绑定，没有授权可到niucloud官网购买云服务之后再进行操作", "toBind": "绑定授权", "toNiucloud": "去niucloud官网", "descriptionLeft": "暂无任何应用，马上去", "buyDescriptionLeft": "您还没有购买过应用，马上去", "link": "官方应用市场", "descriptionRight": "逛逛", "installed-empty": "暂未安装任何应用", "recentlyUpdatedEmpty": "暂无最近更新应用", "siteAddressTips": "授权域名不匹配", "authCodePlaceholder": "请输入授权码", "authSecretPlaceholder": "请输入授权秘钥", "updateCode": "重新绑定", "notHaveAuth": "还没有授权？去购买", "authInfoTips": "授权码和授权秘钥可在Niucloud官网我的授权 授权详情中查看", "addonUninstall": "插件卸载", "appIdentification": "应用标识", "tipText": "标识指开发应用或插件的文件夹名称", "uninstallTips": "是否要卸载该插件？", "upgrade": "升级", "newVersion": "最新版本", "cloudBuild": "云编译", "cloudBuildTips": "是否要进行云编译该操作可能会影响到正在访问的客户是否要继续操作？", "deleteAddonTips": "删除插件会把插件目录连同文件全部删除，确定要删除吗？", "batchUpgrade": "批量升级"}