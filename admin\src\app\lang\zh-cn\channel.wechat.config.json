{"wechatName": "公众号名称", "wechatOriginal": "公众号原始ID", "wechatQrcode": "公众号二维码", "wechatQrcodeTips": "建议尺寸上传430px*430px宽高的二维码", "wechatAppid": "开发者ID", "wechatAppidTips": "开发者ID是公众号开发识别码，配合开发者密码可调用公众号的接口能力。", "wechatAppsecret": "开发者密码", "wechatAppsecretTips": "开发者密码是校验公众号开发者身份的密码，具有极高的安全性", "businessDomain": "业务域名", "jsSecureDomain": "JS接口安全域名", "webAuthDomain": "网页授权域名", "wechatInfo": "公众号信息", "wechatDevelopInfo": "公众号开发信息", "theServerSetting": "消息推送配置", "functionSetting": "功能设置", "functionSettingTips": "在微信公众平台中，点击 设置与开发>公众号设置>功能设置，设置业务域名 JS接口安全域名 网页授权域名", "tokenTips": "必须为英文或数字，长度为3-32字符。", "encodingAESKeyTips": "消息加密密钥由43位字符组成，可随机修改，字符范围为A-Z，a-z，0-9。", "encryptionType": "消息加解密方式", "cleartextMode": "明文模式", "compatibleMode": "兼容模式", "safeMode": "安全模式（推荐）", "wechatNamePlaceholder": "请输入公众号名称", "wechatOriginalPlaceholder": "请输入公众号原始ID", "appidPlaceholder": "请输入开发者ID", "appSecretPlaceholder": "请输入开发者密码", "tokenPlaceholder": "请输入Token", "encodingAesKeyPlaceholder": "请输入EncodingAESKey", "cleartextModeTips": "明文模式下，不使用消息体加解密功能，安全系数较低", "compatibleModeTips": "兼容模式下，明文、密文将共存，方便开发者调试和维护", "safeModeTips": "安全模式下，消息包为纯密文，需要开发者加密和解密，安全系数高"}