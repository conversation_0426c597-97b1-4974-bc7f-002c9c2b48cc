{"title": "插件名称", "titlePlaceholder": "请输入插件名称", "icon": "插件图标", "iconPlaceholder": "请上传插件图标", "iconPlaceholder1": "建议图片尺寸：200*200像素；图片格式：jpg、png、jpeg", "key": "插件标识", "keyPlaceholder": "请输入插件标识", "keyPlaceholderErr": "插件标识格式不正确，只能以字母开头且只能输入字母、数字、下划线", "keyPlaceholder1": "插件标识指开发插件的文件夹名称，申请之后不能修改(仅允许使用字母、数字与下划线组合，且必须以字母开头，同时名称中至少包含一个下划线，格式如：a11_34、f11_22)", "keyPlaceholder2": "插件标识设置后建议进行插件标识检测，如果当前插件标识已经在niucloud官方市场注册，则只能在本地使用，无法在官方市场发布销售", "desc": "插件描述", "descPlaceholder": "请输入插件描述", "author": "作者", "authorPlaceholder": "请输入作者", "version": "版本号", "versionPlaceholder": "请输入版本号", "versionPlaceholderErr": "版本号格式不正确", "versionPlaceholder1": "请注意版本号输入格式，例如: 1.1.1", "cover": "插件封面", "coverPlaceholder": "请上传插件封面", "coverPlaceholder1": "建议图片尺寸：480*240像素；图片格式：jpg、png、jpeg", "type": "插件类型", "typePlaceholder": "请选择插件类型", "typePlaceholder1": "应用：指独立开发的系统，比如商城，零售，erp等", "typePlaceholder2": "插件:指不是独立的系统，可以是辅助应用的插件比如商城的拼团，也可以是独立的插件比如万能表单等", "supportType": "所属应用", "supportApp": "支持应用", "supportAppPlaceholder": "请选择支持应用", "GeneratePlugins": "生成插件", "successText": "检测当前插件标识尚未在应用市场注册，插件开发后可以在niucloud官方市场发布", "warningText": "检测到当前插件标识已经在niucloud官方市场注册，开发的插件只能在本地使用，无法在官方市场发布销售", "onSaveSuccessText": "插件生成成功"}