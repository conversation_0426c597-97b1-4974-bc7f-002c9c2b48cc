{"operatePrompt": "操作提示", "operatePromptTipsOne": "下载之后需使用微信开发者工具上传代码，微信开发者工具下载地址", "operatePromptTipsTwo": "上传之后登录微信公众平台，在版本管理中选择刚上传的版本提交审核，审核通过之后即可发布小程序。", "weappName": "小程序名称", "weappId": "小程序ID", "weappOriginalId": "小程序原始ID", "weappInfo": "小程序信息", "versionCode": "小程序代码", "version": "版本", "down": "下载", "codeDown": "代码下载", "downWeappCode": "下载代码", "weappTips": "请先完善小程序配置", "codeDownOneTips": "下载小程序代码包", "codeDownTwoDesc": "上传代码", "codeDownTwoTips": "下载完成之后，使用微信开发工具进行上传", "codeDownThreeDesc": "发布小程序", "codeDownThreeTips": "上传之后提交审核，审核通过发布小程序", "weappVersion": "小程序版本", "close": "关闭", "code": "版本号", "codePlaceholder": "请输入版本号", "path": "小程序代码", "pathPlaceholder": "请上传小程序代码", "content": "版本说明", "weappAccessFlow": "接入流程", "subscribeMessage": "订阅消息", "weappRelease": "版本管理", "contentPlaceholder": "请输入版本说明", "uploadingTips": "小程序代码上传中", "status": "状态", "preview": "预览", "authTips": "上传代码需先绑定授权码，如果已有授权请先进行绑定，没有授权可到niucloud官网购买云服务之后再进行操作", "toBind": "绑定授权", "toNiucloud": "去niucloud官网", "failReason": "失败原因", "toSetting": "去配置", "cloudRelease": "一键云端发布", "localRelease": "本地发布", "localInsertTips": "请先将uni-app编译成微信小程序，然后使用微信开发者工具进行上传", "uploadSuccessTips": "小程序上传成功后还需到<a href='https://mp.weixin.qq.com/' target='_blank' class='text-primary'>微信公众平台</a>提交审核，审核通过后发布才算正式上线。", "knownToKnow": "我已知晓，不需要再次提示", "siteAuthTips": "上传代码需先绑定授权码，请联系平台管理员进行绑定", "againUpload": "重新上传", "uploadWeapp": "上传小程序", "undoAudit": "撤回审核", "undoAuditTips": "撤回代码审核，单个账号每天审核撤回次数最多不超过 5 次（每天的额度从0点开始生效），一个月不超过 10 次。是否要继续撤回？"}