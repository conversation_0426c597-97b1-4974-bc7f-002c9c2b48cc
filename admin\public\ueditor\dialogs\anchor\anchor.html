<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>
    <style type="text/css">
        * {
            color: #838383;
            margin: 0;
            padding: 0
        }

        html, body {
            font-size: 12px;
            overflow: hidden;
        }

        .content {
            padding: 5px 0 0 15px;
        }

        input {
            margin-left: 4px;
            box-sizing: border-box;
            width: 210px;
            height: 30px;
            line-height: 30px;
            border: 1px solid #d7d7d7;
            border-radius: 3px;
            padding: 0 5px;
            outline: none;
        }
    </style>
</head>
<body>
<div class="content">
    <span><var id="lang_input_anchorName"></var></span><input id="anchorName" value=""/>
</div>
<script type="text/javascript" src="../internal.js?aea0c61c"></script>
<script type="text/javascript">
    var anchorInput = $G('anchorName'),
        node = editor.selection.getRange().getClosedNode();
    if (node && node.tagName == 'IMG' && (node = node.getAttribute('anchorname'))) {
        anchorInput.value = node;
    }
    anchorInput.onkeydown = function (evt) {
        evt = evt || window.event;
        if (evt.keyCode == 13) {
            editor.execCommand('anchor', anchorInput.value);
            dialog.close();
            domUtils.preventDefault(evt)
        }
    };
    dialog.onok = function () {
        editor.execCommand('anchor', anchorInput.value);
        dialog.close();
    };
    $focus(anchorInput);
</script>
</body>
</html>
