.ignore-draggable-element, .draggable-element {
  &.decorate {
    &:hover:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 4rpx dotted $u-primary;
      z-index: 10;
      pointer-events: none;
      cursor: move;
    }

    &.selected:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 4rpx solid $u-primary;
      z-index: 10;
      pointer-events: none;
      cursor: move;
    }
  }
}
