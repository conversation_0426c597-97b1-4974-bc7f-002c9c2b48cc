{"name": "admin", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && node publish.cjs", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@highlightjs/vue-plugin": "2.1.0", "@types/lodash-es": "4.17.6", "@vueuse/core": "9.12.0", "axios": "1.4.0", "crypto-js": "4.1.1", "css-color-function": "1.3.3", "day": "^0.0.2", "echarts": "5.4.1", "element-plus": "^2.7.4", "highlight.js": "11.0.1", "lodash-es": "4.17.21", "nprogress": "0.2.0", "pinia": "2.0.30", "qrcode": "1.5.1", "sass": "1.58.0", "sortablejs": "1.15.0", "vditor": "^3.10.9", "vue": "3.2.45", "vue-i18n": "9.2.2", "vue-jsonp": "2.0.0", "vue-router": "4.1.6", "vue-ueditor-wrap": "^3.0.8", "vue-web-terminal": "3.2.2", "vue3-video-play": "1.3.1-beta.6"}, "devDependencies": {"@tailwindcss/line-clamp": "0.4.2", "@types/qrcode": "1.5.0", "@types/sortablejs": "1.15.0", "@typescript-eslint/eslint-plugin": "5.53.0", "@vitejs/plugin-vue": "4.0.0", "autoprefixer": "10.4.13", "eslint": "8.34.0", "eslint-config-standard-with-typescript": "34.0.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-n": "15.6.1", "eslint-plugin-promise": "6.1.1", "eslint-plugin-vue": "9.9.0", "postcss": "8.4.21", "tailwindcss": "3.2.4", "typescript": "4.9.5", "unplugin-auto-import": "0.13.0", "unplugin-vue-components": "0.23.0", "vite": "4.1.0", "vue-tsc": "1.0.24"}}