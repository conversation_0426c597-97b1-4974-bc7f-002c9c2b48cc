{"tableName": "表名称", "tableContent": "描述", "addon": "插件", "moduleName": "模块名", "className": "类名", "editType": "编辑方式", "createTime": "创建时间", "updateTime": "更新时间", "popup": "弹出", "page": "新页面", "tableNamePlaceholder": "请输入表名", "tableContentPlaceholder": "请输入描述", "addonPlaceholder": "请选择插件", "moduleNamePlaceholder": "请输入模块名", "classNamePlaceholder": "请输入类名", "editTypePlaceholder": "请选择编辑方式", "addCode": "导入数据表", "updateCode": "编辑代码生成", "codeDeleteTips": "确定要删除吗？", "tableComment": "表描述", "tableCreateTime": "创建时间", "tableUpdateTime": "修改时间", "addBtn": "添加", "searchPlaceholder": "请输入表名或表描述搜索", "selectTableTips": "确认导入该数据表吗？", "download": "下载代码", "codeGeneration": "代码生成", "codeList": "生成列表", "step1": "选择数据表生成代码", "describe1": "点击添加，选择对应数据表，添加之后会跳转到对应生成代码的配置管理页面", "btn1": "添加", "step2": "基础设置", "describe2": "代码生成查看基础设置，包括基础的表名、描述、所属插件设置", "btn2": "插件列表", "step3": "字段设置", "describe3": "代码中字段的管理，包括字段是否进行增加、编辑、列表、查询展示等", "btn3": "查看教程", "step4": "页面设置", "describe4": "正在开发中，会将字段进行展示管理配置，同时提供预览，真正实现自定义表单配置", "btn4": "插件列表", "step5": "生成配置", "describe5": "针对代码中展示页面配置，包括单独页面编辑表单还是弹框，生成的代码穿插到系统中还是进行下载等", "btn5": "官方市场", "saveAndSync": "同步代码", "saveAndSyncText": "同步的代码与项目产生冲突，是否确认覆盖?", "saveAndSyncText1": "同步的代码会加入到项目代码中，是否确认继续", "addonName": "所属插件"}