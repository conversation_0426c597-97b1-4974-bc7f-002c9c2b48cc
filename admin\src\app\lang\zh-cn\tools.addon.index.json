{"developmentProcess": "开发流程", "pluginList": "插件列表", "addAddon": "新建插件", "title": "插件名称", "titlePlaceholder": "请输入插件名称", "author": "作者", "authorPlaceholder": "请输入作者", "key": "插件标识", "type": "插件类型", "version": "版本号", "status": "状态", "codeDeleteTips": "删除插件后对应文件会一并删除，是否确认", "step1": "新建一个插件", "describe1": "点击新建插件，生成插件后系统会生成对应插件的基础代码", "btn1": "新建插件", "step2": "安装插件", "describe2": "插件创建之后处于未安装状态，点击进入插件列表选择对应新建插件点击安装", "btn2": "插件列表", "step3": "开发插件", "describe3": "插件安装成功之后就可以进行开发，具体查看开发教程", "btn3": "查看教程", "step4": "打包插件", "describe4": "插件开发的前端代码是直接在对应开发环境运行的，并没有放入插件对应目录，点击打包后会将对应插件的代码整合到插件目录方便后期安装与打包发行版本", "btn4": "插件列表", "step5": "上传到云市场", "describe5": "插件打包成功之后可以上传到官方云市场进行销售，可以打包后选择下载代码zip格式，然后在官网开发商引用选择上传版本", "btn5": "官方市场", "addonDownloadText": "插件打包成功，是否下载"}