{"status": "是否启用", "name": "支付类型", "wechatpay": "微信支付", "alipay": "支付宝支付", "paydesc": "支付方式描述", "wechatpayDesc": "微信支付，用户通过扫描二维码、微信内打开商品页面购买等多种方式调起微信支付模块完成支付。", "alipayDesc": "支付宝网站(www.alipay.com) 是国内先进的网上支付平台。", "config": "设置", "updateWechat": "微信支付", "updateAlipay": "支付宝支付", "updateOfflinepay": "线下支付", "mchId": "商户号", "mchIdPlaceholder": "请输入商户号", "mchIdTips": "微信支付商户号（MCHID）", "mchSecretKey": "APIv3密钥", "mchSecretKeyPlaceholder": "请输入APIv3密钥", "mchSecretKeyTips": "微信支付商户APIv3密钥（paySignKey）", "mchSecretCert": "商户私钥", "mchSecretCertPlaceholder": "请上传商户私钥", "mchSecretCertTips": "微信支付API证书（apiclient_key.pem）", "mchPublicCertPath": "商户公钥", "mchPublicCertPathPlaceholder": "请上传商户公钥", "mchPublicCertPathTips": "微信支付API证书（apiclient_cert.pem）", "appId": "支付宝应用ID", "appIdPlaceholder": "请输入支付宝应用ID", "appSecretCert": "应用私钥", "appSecretCertPlaceholder": "请输入应用私钥", "appPublicCertPath": "应用公钥", "appPublicCertPathPlaceholder": "请上传应用公钥", "alipayPublicCertPath": "支付宝公钥", "alipayPublicCertPathPlaceholder": "请上传支付宝公钥", "alipayRootCertPath": "支付宝根证书", "alipayRootCertPathPlaceholder": "请上传支付宝根证书", "appIdTips": "支付宝分配给开发者的应用ID", "appPublicCertPathTips": "上传appCertPublicKey文件", "alipayPublicCertPathTips": "上传alipayCertPublicKey文件", "alipayRootCertPathTips": "上传alipayRootCert文件", "payType": "支付方式", "templateName": "配置信息", "settingDefaultPay": "设置默认支付", "settingDefault": "设置默认", "defaultTamplate": "请选择模板", "isEnable": "是否启用", "onState": "开启状态", "configurePaymentMethod": "请先配置该支付方式", "enablePaymentMode": "请先开启该支付方式", "clickConfigure": "点击配置", "setConfig": "设置支付配置", "open": "已开启", "notOpen": "未开启", "cancel": "取消", "collectionName": "收款账户名称", "collectionBank": "收款银行", "collectionAccount": "收款账号", "collectionDesc": "转账说明", "collectionNamePlaceholder": "请输入收款账户名称", "collectionBankPlaceholder": "请输入收款银行", "collectionAccountPlaceholder": "请输入收款账号", "collectionDescPlaceholder": "请输入转账说明", "jsapiDir": "JSAPI支付授权目录", "jsapiDirTips": "需在微信商户号>产品中心>开发配置>支付配置 支付授权目录中添加该链接", "h5Domain": "H5支付域名", "h5DomainTips": "需在微信商户号>产品中心>开发配置>支付配置 H5支付域名中添加该域名", "nativeDomain": "Native支付回调链接", "nativeDomainTips": "需在微信商户号>产品中心>开发配置>支付配置 Native支付回调链接中添加该链接", "wechatpayPublicCert": "微信支付公钥", "wechatpayPublicCertId": "微信支付公钥ID", "updateFriendsPay": "找朋友帮忙付", "friendsPaySwitch": "帮付说明", "friendsPayTitle": "帮付说明标题", "friendsPayTitlePlaceholder": "请输入帮付说明标题", "desContent": "说明内容", "friendsPayGoodsSwitch": "订单信息清单", "friendsPayGoodsSwitchTips": "开启后，帮付人可以看到订单信息清单", "friendsPayName": "支付方式名称", "friendsPayNamePlaceholder": "请输入支付方式名称", "desContentPlaceholder": "请输入说明内容", "helpName": "帮付页面名称", "helpNamePlaceholder": "请输入帮付页面名称", "helpBtn": "帮付按钮名称", "helpBtnPlaceholder": "请输入帮付按钮名称", "remark": "发起帮付留言", "remarkPlaceholder": "请输入留言备注", "payWechatImage": "默认分享图片（公众号）", "payWeappImage": "默认分享图片（小程序）"}