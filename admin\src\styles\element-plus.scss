:root {}

.el-header {
    --el-header-padding: 0 !important;
    --el-header-height: 64px !important;
}

/** 卡片面板 **/
.el-card {
    border-radius: 0 !important;
}

/** 表格 **/
.el-table {
    --el-table-header-bg-color: #f5f7f9 !important;
    --el-table-header-text-color: #333333 !important;

    thead,
    thead th {
        font-weight: normal !important;
    }

    // 修改表格中上传图片样式冲突的问题
    .el-table__cell {
        position: inherit !important;
    }
}

/** 文本框 **/
.el-input__wrapper, .el-input-group__append, .el-textarea__inner {
    border-radius: 0 !important;
}

.el-textarea__inner {

    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 3px;
        -moz-border-radius: 3px;
        -webkit-border-radius: 3px;
        background-color: rgb(192, 196, 204);
        opacity: .3;
    }

    &::-webkit-scrollbar-track {
       background-color: transparent ;
    }
}

/** 按钮 **/
.el-button {
    background-color: var(--el-button-bg-color, var(--el-color-white));
}

.el-button:not(.is-round) {
    border-radius: 2px !important;
}
.el-slider {
    --el-slider-button-size: 15px !important;
}

/** 下拉框 **/
.el-select__wrapper {
    border-radius: 0 !important;
}

.el-form-item {
    & > .el-input, .el-cascader, .el-select, .el-date-editor, .el-autocomplete {
        width: 214px;
    }
}

.el-dropdown .el-tooltip__trigger {
    outline: none!important;
}
