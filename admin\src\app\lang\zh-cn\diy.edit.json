{"templatePagePlaceholder": "选择模板", "templatePageEmpty": "无", "changeTemplatePageTips": "切换模板后，当前页面内容将被替换且不被保存，请谨慎操作", "developTitle": "开发环境配置", "wapDomain": "wap域名（WAP_DOMAIN）", "wapDomainPlaceholder": "请输入wap域名", "pageSet": "页面设置", "tabEditContent": "内容", "tabEditStyle": "样式", "pageStyle": "页面样式", "pageContent": "页面内容", "statusBarContent": "导航栏内容", "statusBarStyle": "导航栏样式", "statusBarSwitchTips": "此处控制当前页面导航栏是否显示", "bottomNavContent": "底部导航内容", "popWindowAds": "弹窗广告", "popAdsLink": "广告链接", "popAdsImage": "广告图", "popAdsType": "显示类型", "popAdsIsEnabled": "是否显示", "firstPop": "首次弹出", "popWindowCountTips": "建议上传图片大小：290px * 410px", "everyTimePops": "每次弹出", "diyPageTitle": "页面名称", "diyPageTitlePlaceholder": "请输入页面名称", "pageTitleTips": "页面名称用于后台显示", "diyTitle": "页面标题", "diyTitlePlaceholder": "请输入页面标题", "titleTips": "页面标题用于前台显示", "pageBgColor": "页面颜色", "bgUrl": "背景图片", "bgHeightScale": "高度比例", "bgHeightScaleTip": "为0时背景高度自适应展示", "marginSet": "边距设置", "componentStyleTitle": "组件样式", "bottomBgColor": "底部背景", "bottomBgTips": "底部背景包含边距和圆角", "componentBgColor": "组件背景色", "componentBgUrl": "组件背景图", "componentBgAlpha": "透明度", "bgGradientAngle": "渐变角度", "topToBottom": "从上到下", "leftToRight": "从左到右", "marginTop": "上边距", "marginBottom": "下边距", "marginBoth": "左右边距", "topRounded": "上圆角", "bottomRounded": "下圆角", "warmPrompt": "温馨提示", "leavePageTitleTips": "确定离开此页面？", "leavePageContentTips": "系统可能不会保存您所做的更改。", "decorating": "正在装修", "preview": "保存并预览", "moveUpComponent": "上移", "moveDownComponent": "下移", "copyComponent": "复制", "delComponent": "删除", "resetComponent": "重置", "tabbar": "底部导航", "tabbarSwitchTips": "此处控制当前页面底部导航菜单是否显示", "link": "链接地址", "delComponentTips": "确认要删除当前组件吗？", "notCopy": "无法复制", "componentCanOnlyAdd": "组件只能添加", "piece": "个", "componentNotMoved": "该组件禁止移动", "resetComponentTips": "确认要重置组件默认数据吗？", "image": "图片上传", "imageUpload": "图片上传", "imageSet": "图片设置", "imageAdsTips": "建议上传尺寸相同的图片，推荐尺寸750*350", "imageAdsSameScreenTips": "开启沉浸式样式，请确保该图片广告组件在页面中位于最顶端；为保证体验，请不要开导航栏；沉浸式样式仅在微信小程序中生效。", "sameScreen": "沉浸式", "addImageAd": "添加图片", "imageUrlTip": "请上传图片", "imageHeight": "图片高度", "imageHeightPlaceholder": "请输入图片高度", "imageHeightRegNum": "图片高度格式错误，请输入数字", "dataSources": "数据来源", "defaultSources": "默认", "manualSelectionSources": "手动选择", "selectPlaceholder": "请选择", "selected": "已选", "graphicNavModeTitle": "导航模式", "layoutMode": "排版模式", "layoutModeHorizontal": "横排", "layoutModeVertical": "竖排", "graphicNavSelectMode": "选择模式", "graphicNavModeGraphic": "图文导航", "graphicNavModeImg": "图片导航", "graphicNavModeText": "文字导航", "graphicNavImageSet": "图片设置", "graphicNavImageSize": "图片大小", "graphicNavAroundRadius": "图片圆角", "graphicNavShowStyle": "展示风格", "graphicNavStyleFixed": "固定显示", "graphicNavStyleSingleSlide": "单行滑动", "graphicNavStyleMultiLine": "多行滑动", "graphicNavStylePageSlide": "分页滑动", "graphicNavRowCount": "每行数量", "graphicNavPageCount": "显示方式", "graphicNavSetLabel": "导航设置", "singleLine": "单行", "multiline": "多行", "graphicNavTips": "建议上传尺寸相同的图片，推荐尺寸60*60", "graphicNavTitle": "标题", "graphicNavTitlePlaceholder": "请输入标题", "subGraphicNavTitle": "副标题", "subGraphicNavTitlePlaceholder": "请输入副标题", "subGraphicNavTitleLink": "副标题链接", "addGraphicNav": "添加导航", "blankHeightSet": "高度设置", "blankHeight": "空白高度", "styleSet": "风格设置", "titleStyle": "标题样式", "selectStyle": "风格选择", "activeCubeBlockBtnText": "按钮文字", "btnTextItalics": "斜体", "btnTextNormal": "常规", "styleLabel": "风格", "styleShowTips": "风格 1 2 3，仅在小程序中展示", "titleContent": "标题内容", "title": "标题名称", "titlePlaceholder": "请输入标题", "textAlign": "对齐方式", "textAlignLeft": "居左", "textAlignCenter": "居中", "textSet": "文字设置", "textFontSize": "文字大小", "textFontWeight": "文字粗细", "fontWeightBold": "加粗", "fontWeightNormal": "常规", "textColor": "文字颜色", "subTitleStyle": "副标题样式", "subTextBgColor": "背景色", "subTitleContent": "标题内容", "subTitle": "副标题", "subTitlePlaceholder": "请输入副标题", "moreContent": "“更多”按钮内容", "more": "文字", "morePlaceholder": "请输入文字", "moreIsShow": "是否显示", "memberStyle": "会员样式", "template": "模板", "imageGap": "图片间隙", "rubikCubeStyle": "魔方样式", "rubikCubeLayout": "魔方布局", "hotArea": "热区", "hotAreaSet": "热区设置", "hotAreaBackground": "热区背景", "addHotArea": "添加热区", "clickSet": "点击设置", "selectedAfterHotArea": "个热区", "hotAreaManage": "热区管理", "selectedHotArea": "请选择热区", "hotAreaLink": "的链接地址", "addonListSet": "应用设置", "addonListTips": "应用选择", "selectAddonTips": "请选择应用", "addonTitle": "应用名称", "addonDesc": "应用描述", "addonIcon": "应用图标", "selectAddon": "选择应用", "addAddon": "添加应用", "show": "显示", "hidden": "隐藏", "goodsCategoryTitle": "商品分类", "customGoods": "手动选择", "goodsNum": "商品数量", "selectCategory": "选择分类", "categoryName": "分类名称", "categoryImage": "分类图片", "selectSource": "选择数据源", "richTextContentSet": "内容设置", "richTextPlaceholder": "请输入富文本内容", "activeCubeBlockContent": "板块内容", "activeCubeTitle": "标题", "activeCubeTitlePlaceholder": "请输入标题", "activeCubeSubTitle": "副标题", "activeCubeSubTitlePlaceholder": "请输入副标题", "activeCubeButton": "按钮", "activeCubeButtonPlaceholder": "请输入按钮文字", "activeCubeButtonColor": "按钮颜色", "activeListFrameColor": "框体颜色", "activeCubeSubTitleTextColor": "文字颜色", "activeCubeSubTitleBgColor": "背景颜色", "activeCubeAddItem": "添加一个板块", "activeCubeBlockStyle": "板块样式", "activeCubeBlockTextFontWeight": "标题粗细", "noticeStyle": "公告风格", "noticeType": "类型", "noticeTypeImg": "图片", "noticeTypeText": "文字", "noticeTypeTextPlaceholder": "请输入公告标题", "noticeTitle": "公告标题", "addNotice": "添加公告", "noticeText": "公告内容", "noticeScrollWay": "滚动方式", "noticeUpDown": "上下滚动", "noticeHorizontal": "横向滚动", "noticeShowType": "点击类型", "noticeShowPopUp": "弹出公告内容", "noticeShowLink": "跳转链接", "dragMouseAdjustOrder": "鼠标拖拽可调整顺序", "noticePlaceholderText": "请输入公告内容", "carouselSearchShowPosition": "显示设置", "carouselSearchOpen": "开启", "carouselSearchClose": "关闭", "carouselSearchBgGradient": "背景渐变", "carouselSearchShowWay": "展示方式", "carouselSearchShowWayStatic": "正常显示", "carouselSearchShowWayFixed": "滚动至顶部固定", "carouselSearchFixedBgColor": "置顶背景", "carouselSearchStyleSelect": "风格选择", "carouselSearchSet": "搜索设置", "carouselSearchSubTitle": "副标题", "carouselSearchSubTitleStyle": "副标题样式", "carouselSearchPositionStyle": "定位样式", "carouselSearchSubTitlePlaceholder": "请输入副标题内容", "carouselSearchText": "搜索内容", "carouselSearchTextColor": "文字颜色", "carouselSearchBgColor": "背景颜色", "carouselSearchBtnColor": "按钮颜色", "carouselSearchBtnBgColor": "按钮背景色", "carouselSearchHotWordSet": "搜索热词", "carouselSearchHotWordInterval": "显示时间 / 秒", "carouselSearchHotWordText": "内容", "carouselSearchHotWordTextPlaceholder": "请输入热词", "carouselSearchAddHotWordItem": "添加一个热词", "carouselSearchLogoTips": "建议尺寸，70px * 30px", "carouselSearchTextTips": "搜索内容是默认展示数据，当添加搜索热词时，搜索内容隐藏; 当没有搜索热词时，搜索内容展示", "carouselSearchPlaceholder": "请输入搜索内容", "carouselSearchTabSet": "选项卡设置", "carouselSearchTabControl": "展示开关", "carouselSearchTabCategoryText": "分类名称", "carouselSearchTabCategoryTextPlaceholder": "请输入分类名称", "carouselSearchAddTabItem": "添加一个选项卡", "selectSourcesDiyPage": "选择微页面", "selectDiyPagePlaceholder": "请选择微页面", "diyPageTypeName": "页面类型", "diyPageForAddon": "所属应用", "carouselSearchSwiperSet": "轮播图设置", "carouselSearchSwiperControl": "展示开关", "carouselSearchSwiperInterval": "切换间隔 / 秒", "carouselSearchSwiperTips": "建议上传尺寸相同的图片，推荐尺寸750*350；鼠标拖拽可调整图片顺序", "carouselSearchTabStyle": "选项卡样式", "carouselSearchStyle": "搜索框样式", "noColor": "常规颜色", "selectColor": "选中颜色", "fixedNoColor": "下滑常规颜色", "fixedSelectColor": "下滑选中颜色", "carouselSearchSwiperIndicatorSet": "指示器设置", "carouselSearchSwiperIndicatorStyle": "指示器样式", "carouselSearchSwiperStyle": "轮播样式", "carouselSearchSwiperIndicatorStyle1": "样式1", "carouselSearchSwiperIndicatorStyle2": "样式2", "carouselSearchSwiperIndicatorStyle3": "样式3", "carouselSearchSwiperIndicatorAlign": "显示位置", "alignLeft": "居左", "alignCenter": "居中", "alignRight": "居右", "horzLineStyle": "线条风格", "horzLineStyleSolid": "实线", "horzLineStyleDashed": "虚线", "horzLineBorderColor": "线条颜色", "horzLineBorderWidth": "线条宽度", "floatBtnButton": "按钮位置", "floatBtnOffset": "上下偏移", "lateralBtnOffset": "左右偏移", "floatBtnImageSet": "图片设置", "floatBtnImageSize": "图片大小", "floatBtnAroundRadius": "图片圆角", "floatBtnImageSuggest": "建议上传正方形图片", "topStatusBarImg": "图片", "topStatusBarNav": "导航栏", "topStatusBarNavTips": "此处控制当前页面导航栏是否显示", "topStatusBarImgTips": "宽度自适应（最大150px），高度28px", "topStatusBarTextColor": "标题颜色", "topStatusBarBgColor": "头部颜色", "rollTopStatusBarBgColor": "滚动后头部颜色", "rollTopStatusBarTextColor": "滚动后标题颜色", "topStatusBarSearchName": "搜索内容", "topStatusBarSearchNamePlaceholder": "请输入搜索关键词", "settingTips": "点击查看如何配置", "pictureShowBlockOne": "模块一", "pictureShowBlockTwo": "模块二", "subTitleTextColor": "标题颜色", "pictureShowBgColor": "背景颜色", "pictureShowBtnText": "按钮文字", "pictureShowBtnColor": "文字颜色", "pictureShowBtnBgColor": "背景颜色", "pictureShowBlockStyle": "模块样式"}