NODE_ENV = 'development'

# api请求地址
VITE_APP_BASE_URL='https://1.92.155.114/api'

# 图片服务器地址
VITE_IMG_DOMAIN='https://1.92.155.114'

# 站点id 仅在编译为小程序时生效
VITE_SITE_ID = '100000'

# 本地存储时token的参数名
VITE_REQUEST_STORAGE_TOKEN_KEY='wapToken'

# 请求时header中token的参数名
VITE_REQUEST_HEADER_TOKEN_KEY='token'

# 请求时header中站点的参数名
VITE_REQUEST_HEADER_SITEID_KEY='site-id'

# 请求时header中来源场景的参数名
VITE_REQUEST_HEADER_CHANNEL_KEY='channel'

# 应用版本
VITE_APP_VERSION='1.0.1'
