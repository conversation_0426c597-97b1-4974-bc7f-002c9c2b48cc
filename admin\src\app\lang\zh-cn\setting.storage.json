{"name": "存储方式", "aliStorage": "阿里云存储", "localStorage": "本地存储", "qiniuStorage": "七牛云存储", "tencentStorage": "腾讯云存储", "isUse": "是否启用", "config": "设置", "aliBucket": "存储空间", "aliAccessKey": "AccessKeyID", "aliSecretKey": "AccessKeySecret", "aliEndpoint": "Endpoint", "domain": "空间域名", "domainPlaceholder": "请输入空间域名", "aliBucketPlaceholder": "请输入存储空间", "aliAccessKeyPlaceholder": "请输入AccessKeyID", "aliSecretKeyPlaceholder": "请输入AccessKeySecret", "aliEndpointPlaceholder": "请输入Endpoint", "aliBucketTips": "存储空间与阿里云OSS开通对象名称一致", "aliAccessKeyTips": "填写阿里云Access Key管理的(ID)。", "aliSecretKeyTips": "Access Key Secret是您访问阿里云API的密钥，具有该账户完全的权限，请您妥善保管。(填写完Access Key ID 和 Access Key Secret 后请选择bucket)", "aliEndpointTips": "Bucket地域endpoint", "aliDomainTips": "域名格式：http://xx.xxxx.com/（不可绑定当前网站域名，建议新开二级域名）", "qiniuBucket": "存储空间", "qiniuAccessKey": "Accesskey", "qiniuSecretKey": "<PERSON><PERSON>", "qiniuBucketTips": "请保证bucket为可公共读取的", "qiniuBucketPlaceholder": "请输入存储空间", "qiniuAccessKeyPlaceholder": "请输入Accesskey", "qiniuSecretKeyPlaceholder": "请输入Secretkey", "tencentBucket": "存储空间", "tencentAccessKey": "SECRET_ID", "tencentSecretKey": "SECRET_KEY", "region": "REGION", "regionPlaceholder": "请输入REGION", "tencentBucketTips": "请保证bucket为可公共读取的", "tencentBucketPlaceholder": "请输入存储空间", "tencentAccessKeyPlaceholder": "请输入SECRET_ID", "tencentSecretKeyPlaceholder": "请输入SECRET_KEY"}