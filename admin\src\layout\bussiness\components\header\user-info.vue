<template>
    <div>
        <el-dropdown @command="clickEvent" :tabindex="1">
            <div class="userinfo flex h-full items-center">
                <el-avatar v-if="userStore.userInfo.head_img" :size="25" :icon="UserFilled" :src="img(userStore.userInfo.head_img)"/>
                <img v-else src="@/app/assets/images/member_head.png" class="w-[25px] rounded-full" />
                <div class="user-name pl-[8px]">{{ userStore.userInfo.username }}</div>
                <icon name="element ArrowDown" class="ml-[5px]" />
            </div>
            <template #dropdown>
                <div class="p-[10px]">
                    <div class="userinfo flex h-full items-center pb-[10px] border-b-[1px] border-solid border-[#e5e5e5]">
                        <el-avatar v-if="userStore.userInfo.head_img" :size="45" :icon="UserFilled" :src="img(userStore.userInfo.head_img)"/>
                        <img v-else src="@/app/assets/images/member_head.png" class="w-[45px] rounded-full" />
                        <div>
                            <div class="user-name pl-[8px] text-[14px]">{{ userStore.userInfo.username }}</div>
                            <div class="pl-[8px] text-[13px] text-[#9699B6]">个人中心</div>
                        </div>
                    </div>
                    <el-dropdown-menu>
                        <el-dropdown-item @click="toLink('/home/<USER>')" v-if="isAllowChange">
                            <div class="flex items-center leading-[1] py-[5px]">
                                <span class="iconfont iconqiehuan ml-[4px] !text-[14px] mr-[10px]"></span>
                                <span class="text-[14px]">切换站点</span>
                            </div>
                        </el-dropdown-item>
                        <el-dropdown-item @click="getUserInfoFn">
                            <div class="flex items-center leading-[1] py-[5px]">
                                <span class="iconfont iconshezhi1 ml-[4px] !text-[14px] mr-[10px]"></span>
                                <span class="text-[14px]">账号设置</span>
                            </div>
                        </el-dropdown-item>
                        <el-dropdown-item @click="changePasswordDialog=true">
                            <div class="flex items-center leading-[1] py-[5px]">
                                <span class="iconfont iconxiugai ml-[4px] !text-[14px] mr-[10px]"></span>
                                <span class="text-[14px]">修改密码</span>
                            </div>
                        </el-dropdown-item>
                        <el-dropdown-item @click="logout">
                            <div class="flex items-center leading-[1] py-[5px]">
                                <span class="iconfont icontuichudenglu ml-[4px] !text-[14px] mr-[10px]"></span>
                                <span class="text-[14px]">退出登录</span>
                            </div>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </div>
            </template>
        </el-dropdown>
        <el-dialog v-model="changePasswordDialog" width="450px" title="修改密码">
            <div>
                <el-form :model="saveInfo" label-width="90px" ref="formRef" :rules="formRules" class="page-form">
                    <el-form-item :label="t('originalPassword')" prop="original_password">
                        <el-input v-model="saveInfo.original_password" type="password" :placeholder="t('originalPasswordPlaceholder')" clearable class="input-width" maxlength="40" />
                    </el-form-item>
                    <el-form-item :label="t('newPassword')" prop="password">
                        <el-input v-model="saveInfo.password" type="password" :placeholder="t('passwordPlaceholder')" clearable class="input-width" maxlength="40" />
                        <div class="form-tip">{{t('passwordTip')}}</div>
                    </el-form-item>
                    <el-form-item :label="t('passwordCopy')" prop="password_copy">
                        <el-input v-model="saveInfo.password_copy" type="password" :placeholder="t('passwordPlaceholder')" clearable class="input-width" maxlength="40" />
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="changePasswordDialog = false">{{t('cancel')}}</el-button>
                    <el-button type="primary" @click="submitForm(formRef)">{{t('save')}}</el-button>
                </span>
            </template>
        </el-dialog>
        <user-info-edit ref="userInfoEditRef" />
    </div>
</template>

<script lang="ts" setup>
import { UserFilled } from '@element-plus/icons-vue'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import type { FormInstance, FormRules, ElNotification } from 'element-plus'
import useUserStore from '@/stores/modules/user'
import { setUserInfo } from '@/app/api/personal'
import { img } from '@/utils/common'
import { t } from '@/lang'
import userInfoEdit from '@/app/components/user-info-edit/index.vue'

const isAllowChange = localStorage.getItem('isAllowChange') === 'true';
const userStore = useUserStore()
const router = useRouter()
const clickEvent = (command: string) => {
    switch (command) {
        case 'logout':
            userStore.logout()
            break
    }
}

const logout = () => {
    userStore.logout()
}
const toLink = (link) => {
    router.push(link)
}
const userInfoEditRef = ref(null)
const getUserInfoFn = () => {
    userInfoEditRef.value?.open()
}
// 修改密码 --- start
const changePasswordDialog = ref(false)
const formRef = ref<FormInstance>()
// 提交信息
const saveInfo = reactive({
    original_password: '',
    password: '',
    password_copy: ''
})

// 表单验证规则
const formRules = reactive<FormRules>({
    original_password: [
        { required: true, message: t('originalPasswordPlaceholder'), trigger: 'blur' }
    ],
    password: [
        { required: true, message: t('passwordPlaceholder'), trigger: 'blur' }
    ],
    password_copy: [
        { required: true, message: t('passwordPlaceholder'), trigger: 'blur' }
    ]
})

const submitForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate((valid) => {
        if (valid) {
            let msg = ''
            if (saveInfo.password && !saveInfo.original_password) msg = t('originalPasswordHint')
            if (saveInfo.password && saveInfo.original_password && !saveInfo.password_copy) msg = t('newPasswordHint')
            if (saveInfo.password && saveInfo.original_password && saveInfo.password_copy && saveInfo.password != saveInfo.password_copy) msg = t('doubleCipherHint')
            if (msg) {
                ElNotification({
                    type: 'error',
                    message: msg
                })
                return
            }

            setUserInfo(saveInfo).then((res: any) => {
                changePasswordDialog.value = false
            })
        } else {
            return false
        }
    })
}
</script>

<style lang="scss" scoped>
.el-popper .el-dropdown-menu{
    width: 150px;
}
</style>
