{"oplatformSetting": "开放平台设置", "appidPlaceholder": "请输入微信第三方平台AppId", "appSecretPlaceholder": "请输入微信第三方平台AppSecret", "tokenPlaceholder": "请输入消息校验Token", "aesKeyPlaceholder": "请输入消息加解密Key", "oplatformComm": "开放平台通信", "empowerStartDomain": "授权发起页域名", "empowerReceiveUrl": "授权事件接收URL", "messageValidationToken": "消息校验Token", "messageDecryptKey": "消息加解密Key", "messageReceiveUrl": "消息与事件接收URL", "wechatDomain": "公众号开发域名", "weappDomain": "小程序服务器域名", "weappBusinessDomain": "小程序业务域名", "oplatformBuilder": "开发者设置", "builderEmail": "开发者邮箱", "builderMobile": "开发者手机号", "builderQQ": "开发者QQ", "builderWx": "开发者微信", "messageDecryptKeyTips": "在代替公众号或小程序收发消息过程中使用。必须是长度为43位的字符串，只能是字母和数字。", "regenerate": "重新生成", "messagesReceiving": "消息与事件接收", "domainSetting": "域名配置", "developerWeappUpload": "开发小程序配置", "developerAppid": "开发小程序appid", "uploadKey": "代码上传密钥", "uploadKeyTips": "", "developAppid": "开发小程序APPID", "developAppidPlaceholder": "请输入开发小程序APPID", "uploadIpTips": "如果小程序代码上传开启了ip白名单设置，在ip白名单中添加ip：", "siteWeappSync": "站点小程序同步", "syncSiteWeappTips": "是否要给该套餐下已授权小程序的站点提交代码？"}