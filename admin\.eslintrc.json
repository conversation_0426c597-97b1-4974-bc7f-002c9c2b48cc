{"env": {"browser": true, "es2021": true}, "extends": ["plugin:vue/vue3-essential", "standard-with-typescript", "eslint:recommended"], "overrides": [], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "parser": "@typescript-eslint/parser"}, "plugins": ["vue", "@typescript-eslint"], "rules": {"no-tabs": "off", "indent": [1, 4, {"SwitchCase": 1}], "eqeqeq": "off", "vue/multi-word-component-names": "off"}}