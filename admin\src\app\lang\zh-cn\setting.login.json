{"commonSetting": "通用设置", "logonMode": "普通注册方式", "isUsername": "账号密码登录", "isUsernameTip": "开启之后可以使用账号+密码进行注册和登录", "isMobile": "手机验证码登录", "isMobileTip": "开启之后可以使用手机+验证码进行注册和登录或者快捷登录/注册", "isBindMobile": "强制绑定手机", "isBindMobileTip": "开启之后，会员注册时会强制绑定手机号，并且在相关页面也会引导会员强制绑定手机账号，否则将影响功能正常使用，方便会员在不同端口统一账号，也方便商家进行管理，已注册会员不受影响", "agreement": "政策协议", "agreementTips": "注册时服务协议和隐私协议是否进行展示", "tripartiteSetting": "第三方设置", "isAuthRegister": "自动注册会员", "isAuthRegisterTip": "开启之后，微信公众号、小程序等第三方平台可以自动注册会员或者快捷登录/注册会员，方便会员自动登录", "isForceAccessUserInfo": "强制获取用户信息", "isForceAccessUserInfoTip": "开启之后，将强制获取用户头像、昵称等信息，需要用户同意后，才能注册成功", "mobileOrUsernameNoEmpty": "普通注册方式至少需启用一种", "loginPageSet": "界面设置", "bgUrl": "背景图", "bgUrlPlaceholder": "建议图片尺寸：750*669像素；图片格式：jpg、png、jpeg", "desc": "描述", "descPlaceholder": "请输入描述"}