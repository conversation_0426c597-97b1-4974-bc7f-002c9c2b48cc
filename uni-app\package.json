{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "node publish.cjs mp-weixin dev", "dev:niu-mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build && node publish.cjs h5 build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin && node publish.cjs mp-weixin build", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-3080720230703001", "@dcloudio/uni-app-plus": "3.0.0-3080720230703001", "@dcloudio/uni-components": "3.0.0-3080720230703001", "@dcloudio/uni-h5": "3.0.0-3080720230703001", "@dcloudio/uni-mp-alipay": "3.0.0-3080720230703001", "@dcloudio/uni-mp-baidu": "3.0.0-3080720230703001", "@dcloudio/uni-mp-jd": "3.0.0-3080720230703001", "@dcloudio/uni-mp-kuaishou": "3.0.0-3080720230703001", "@dcloudio/uni-mp-lark": "3.0.0-3080720230703001", "@dcloudio/uni-mp-qq": "3.0.0-3080720230703001", "@dcloudio/uni-mp-toutiao": "3.0.0-3080720230703001", "@dcloudio/uni-mp-weixin": "3.0.0-3080720230703001", "@dcloudio/uni-quickapp-webview": "3.0.0-3080720230703001", "html2canvas": "^1.4.1", "image-tools": "^1.4.0", "lodash-es": "^4.17.21", "pinia": "2.0.36", "qrcode": "^1.5.1", "qs": "6.7.0", "sortablejs": "^1.15.0", "uview-plus": "^3.1.29", "vue": "^3.3.0", "vue-i18n": "^9.2.2", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@dcasia/mini-program-tailwind-webpack-plugin": "^1.5.6", "@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "3.0.0-3080720230703001", "@dcloudio/uni-cli-shared": "3.0.0-3080720230703001", "@dcloudio/uni-stacktracey": "3.0.0-3080720230703001", "@dcloudio/vite-plugin-uni": "3.0.0-3080720230703001", "@rollup/plugin-commonjs": "^24.0.1", "@types/qrcode": "^1.5.0", "@types/sortablejs": "^1.15.0", "@vue/tsconfig": "^0.1.3", "sass": "^1.54.5", "typescript": "^4.9.4", "vite": "4.0.4", "vite-plugin-windicss": "^1.8.10", "vue-tsc": "^1.0.24", "windicss": "^3.5.6"}}